import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, random_split
import torchvision.transforms as transforms
from transformers import ViTModel, ViTImageProcessor
import pandas as pd
import numpy as np
import os
from PIL import Image
import matplotlib.pyplot as plt
from tqdm import tqdm
from datetime import datetime
import seaborn as sns
from sklearn.metrics import accuracy_score, confusion_matrix, ConfusionMatrixDisplay

# --- Constants ---
PATCH_SIZE = 224
BATCH_SIZE = 4  # Number of patches per WSI to process at once
NUM_EPOCHS = 50
LEARNING_RATE = 1e-5

# --- Load TIL Scores ---
def load_til_scores(csv_path="til_scores_local.csv"):
    if not os.path.exists(csv_path):
        print(f"❌ File not found: {csv_path}")
        return None, None
    df = pd.read_csv(csv_path)
    labels_dict = dict(zip(df['image-id'], df['tils-score']))
    print(f"Loaded CSV with {len(labels_dict)} WSI labels")
    return labels_dict, df['image-id'].tolist()

# --- Custom Dataset ---
class WSIDataset(Dataset):
    def __init__(self, wsi_names, base_dir, labels_dict, processor):
        self.wsi_names = wsi_names
        self.base_dir = base_dir
        self.labels_dict = labels_dict
        self.processor = processor
        self.wsi_patches = {}
        for wsi in wsi_names:
            patch_dir = os.path.join(base_dir, wsi)
            if os.path.isdir(patch_dir):
                patch_files = [f for f in os.listdir(patch_dir) if f.endswith(".tif")]
                if patch_files:
                    self.wsi_patches[wsi] = patch_files
        print(f"Initialized dataset with {len(self.wsi_patches)} WSIs with patches")

    def __len__(self):
        return len(self.wsi_names)

    def __getitem__(self, idx):
        wsi = self.wsi_names[idx]
        patch_files = self.wsi_patches.get(wsi, [])
        patch_paths = [os.path.join(self.base_dir, wsi, f) for f in patch_files]
        images = []
        valid_patch_files = []

        for patch_path, patch_file in zip(patch_paths, patch_files):
            try:
                image = Image.open(patch_path).convert("RGB")
                if image.size != (PATCH_SIZE, PATCH_SIZE):
                    image = image.resize((PATCH_SIZE, PATCH_SIZE))
                images.append(image)
                valid_patch_files.append(patch_file)
            except Exception as e:
                print(f"Error loading {patch_path}: {e}")
                continue

        if not images:
            print(f"No valid images for {wsi}")
            return None

        inputs = self.processor(images=images, return_tensors="pt")
        pixel_values = inputs["pixel_values"]  # Shape: (num_patches, 3, 224, 224)
        til_score = torch.tensor(self.labels_dict.get(wsi, 0.0), dtype=torch.float32)
        return pixel_values, til_score, wsi, valid_patch_files

# --- Custom Collate ---
def custom_collate_fn(batch):
    batch = [item for item in batch if item is not None]
    if len(batch) == 0:
        return None
    pixel_values, til_scores, wsi_names, patch_files = zip(*batch)
    return list(pixel_values), torch.stack(til_scores), list(wsi_names), list(patch_files)

# --- Model ---
class ViTForWSITILPrediction(nn.Module):
    def __init__(self, vit_base):
        super().__init__()
        self.vit = vit_base
        self.regression_head = nn.Linear(768, 1)

    def forward(self, pixel_values):
        wsi_outputs = []
        for patches in pixel_values:
            outputs = self.vit(pixel_values=patches)
            patch_embeddings = outputs.last_hidden_state[:, 0, :]  # CLS tokens: (num_patches, 768)
            wsi_embedding = patch_embeddings.mean(dim=0)  # Average pooling: (768,)
            wsi_output = self.regression_head(wsi_embedding.unsqueeze(0))  # (1, 1)
            wsi_outputs.append(wsi_output)
        return torch.cat(wsi_outputs, dim=0), None, None

# --- Training ---
def train_model(model, train_loader, val_loader, device, num_epochs=NUM_EPOCHS, learning_rate=LEARNING_RATE):
    model.to(device)
    criterion = nn.MSELoss()
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', factor=0.1, patience=2, verbose=True)
    best_loss = float('inf')
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    os.makedirs("vit_checkpoints", exist_ok=True)

    for epoch in range(num_epochs):
        model.train()
        train_loss = 0.0
        for batch in tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Train]"):
            if batch is None:
                continue
            pixel_values, til_scores, _, _ = batch
            pixel_values = [pv.to(device) for pv in pixel_values]
            til_scores = til_scores.to(device)
            outputs, _, _ = model(pixel_values)
            outputs = outputs.squeeze(-1)
            loss = criterion(outputs, til_scores)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            train_loss += loss.item()
        train_loss /= len(train_loader)

        model.eval()
        val_loss = 0.0
        with torch.no_grad():
            for batch in tqdm(val_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Val]"):
                if batch is None:
                    continue
                pixel_values, til_scores, _, _ = batch
                pixel_values = [pv.to(device) for pv in pixel_values]
                til_scores = til_scores.to(device)
                outputs, _, _ = model(pixel_values)
                outputs = outputs.squeeze(-1)
                loss = criterion(outputs, til_scores)
                val_loss += loss.item()
        val_loss /= len(val_loader)

        print(f"Epoch {epoch+1}: Train Loss={train_loss:.4f}, Val Loss={val_loss:.4f}")
        scheduler.step(val_loss)

        torch.save(model.state_dict(), f"vit_checkpoints/vit_epoch_{epoch+1}_{timestamp}.pth")
        if val_loss < best_loss:
            best_loss = val_loss
            torch.save(model.state_dict(), f"vit_checkpoints/vit_best_model_{timestamp}.pth")

    return model

# --- Evaluation ---
def evaluate_and_save_predictions(model, data_loader, device, output_csv="wsi_til_predictions.csv", classification_thresholds=[1, 2]):
    model.eval()
    all_predictions = []

    with torch.no_grad():
        for batch in tqdm(data_loader, desc="Evaluating"):
            if batch is None:
                continue
            pixel_values, til_scores, wsi_names, _ = batch
            pixel_values = [pv.to(device) for pv in pixel_values]
            outputs, _, _ = model(pixel_values)
            outputs = outputs.squeeze(-1).cpu().numpy()
            til_scores = til_scores.numpy()

            for i in range(len(outputs)):
                all_predictions.append({
                    'wsi_name': wsi_names[i],
                    'true_til_score': til_scores[i],
                    'predicted_til_score': outputs[i]
                })

    df = pd.DataFrame(all_predictions)

    os.makedirs("vit_outputs", exist_ok=True)
    df.to_csv(os.path.join("vit_outputs", output_csv), index=False)
    print(f"✅ Saved WSI-level predictions to vit_outputs/{output_csv}")

    def classify(score):
        if score < classification_thresholds[0]:
            return 0
        elif score < classification_thresholds[1]:
            return 1
        else:
            return 2

    df['true_class'] = df['true_til_score'].apply(classify)
    df['predicted_class'] = df['predicted_til_score'].apply(classify)

    accuracy = accuracy_score(df['true_class'], df['predicted_class'])
    cm = confusion_matrix(df['true_class'], df['predicted_class'])
    print(f"✅ WSI-level classification accuracy: {accuracy:.4f}")

    disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=['Low', 'Medium', 'High'])
    disp.plot(cmap=plt.cm.Blues, values_format='d')
    plt.title('Confusion Matrix (WSI-level Classification)')
    plt.tight_layout()
    plt.savefig("vit_outputs/wsi_confusion_matrix.png")
    plt.close()
    print("✅ Saved WSI-level confusion matrix to vit_outputs/wsi_confusion_matrix.png")

    df['error'] = df['predicted_til_score'] - df['true_til_score']
    df['abs_error'] = df['error'].abs()

    y_true = df['true_til_score'].values
    y_pred = df['predicted_til_score'].values

    mse = np.mean((y_pred - y_true) ** 2)
    rmse = np.sqrt(mse)
    mae = np.mean(np.abs(y_pred - y_true))
    pearson_r = np.corrcoef(y_true, y_pred)[0, 1]

    metrics = {
        'MSE': mse,
        'RMSE': rmse,
        'MAE': mae,
        'Pearson_R': pearson_r,
        'Accuracy': accuracy
    }

    with open(os.path.join("vit_outputs", output_csv), "a") as f:
        f.write(f"\n# Metrics\n# MSE: {mse:.4f}\n# RMSE: {rmse:.4f}\n# MAE: {mae:.4f}\n# Pearson_R: {pearson_r:.4f}")

    print("\n📊 WSI-Level Prediction Metrics:")
    for k, v in metrics.items():
        print(f"  {k}: {v:.4f}")

    os.makedirs("vit_outputs/wsi_plots", exist_ok=True)
    for _, row in df.iterrows():
        wsi = row['wsi_name']
        true_score = row['true_til_score']
        pred_score = row['predicted_til_score']

        plt.figure(figsize=(5, 4))
        bars = plt.bar(['True', 'Predicted'], [true_score, pred_score], color=['skyblue', 'salmon'])
        for bar in bars:
            yval = bar.get_height()
            plt.text(bar.get_x() + bar.get_width() / 2.0, yval + 0.05, round(yval, 2), ha='center', va='bottom')
        plt.title(f"TIL Score for {wsi}")
        plt.ylim(0, max(true_score, pred_score) + 1)
        plt.tight_layout()
        plt.savefig(f"vit_outputs/wsi_plots/{wsi}_til_plot.png")
        plt.close()

    sorted_df = df.sort_values(by='predicted_til_score')
    plt.figure(figsize=(12, 6))
    x = range(len(sorted_df))
    plt.bar(x, sorted_df['true_til_score'], width=0.4, label='True TIL', align='center')
    plt.bar([i + 0.4 for i in x], sorted_df['predicted_til_score'], width=0.4, label='Predicted TIL', align='center')
    plt.xticks([i + 0.2 for i in x], sorted_df['wsi_name'], rotation=90)
    plt.xlabel('WSI')
    plt.ylabel('TIL Score')
    plt.title('Predicted vs True TIL Scores (Sorted by Predicted)')
    plt.legend()
    plt.tight_layout()
    plt.savefig("vit_outputs/wsi_til_comparison_sorted.png")
    plt.close()
    print("✅ Saved overall sorted WSI TIL score comparison bar plot")

    plt.figure(figsize=(6, 6))
    sns.regplot(x='true_til_score', y='predicted_til_score', data=df, ci=None, scatter_kws={"s": 60})
    plt.title(f'Pearson Correlation (R = {pearson_r:.2f})')
    plt.xlabel('True TIL Score')
    plt.ylabel('Predicted TIL Score')
    plt.grid(True)
    plt.tight_layout()
    plt.savefig("vit_outputs/pearson_correlation_plot.png")
    plt.close()
    print("✅ Saved Pearson correlation plot to vit_outputs/pearson_correlation_plot.png")

    return df, metrics

# --- Main ---
if __name__ == "__main__":
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    model_name = "google/vit-base-patch16-224"
    processor = ViTImageProcessor.from_pretrained(model_name)
    vit_base = ViTModel.from_pretrained(model_name, add_pooling_layer=False)
    model = ViTForWSITILPrediction(vit_base)

    labels_dict, wsi_names = load_til_scores()
    if not labels_dict:
        exit(1)

    base_dir = "C:/Users/<USER>/Desktop/TILSProj/data"  # Updated to match your project directory
    dataset = WSIDataset(wsi_names, base_dir, labels_dict, processor)
    if len(dataset) == 0:
        print("❌ No valid WSIs found.")
        exit(1)

    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = random_split(dataset, [train_size, val_size])

    train_loader = DataLoader(train_dataset, batch_size=1, shuffle=True, collate_fn=custom_collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=1, shuffle=False, collate_fn=custom_collate_fn)
    full_loader = DataLoader(dataset, batch_size=1, shuffle=False, collate_fn=custom_collate_fn)

    model = train_model(model, train_loader, val_loader, device)
    evaluate_and_save_predictions(model, full_loader, device)